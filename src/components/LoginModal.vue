<template>
  <a-modal
    v-model:open="visible"
    :footer="null"
    :width="400"
    :closable="false"
    :maskClosable="false"
    @cancel="handleCancel"
    class="login-modal"
  >
    <div class="login-modal-header">
      <p class="login-modal-header-label">手机号登录</p>
    </div>
    <div class="login-modal-content">
      <!-- 手机号输入 -->
      <div class="form-item center-form-item">
        <a-input
          v-model:value="formData.phone"
          placeholder="请输入手机号码"
          size="large"
          :maxlength="11"
          class="form-input"
        />
      </div>

      <!-- 验证码输入 -->
      <div class="form-item center-form-item">
        <div class="verification-input">
          <a-input
            v-model:value="formData.smsCode"
            placeholder="请输入短信验证码"
            size="large"
            :maxlength="6"
            class="form-input"
          >
            <template #suffix>
              <a-button
                :disabled="!canSendSms"
                :loading="sendingSms"
                type="text"
                @click="handleSendSms"
                style=""
              >
                {{ smsButtonText }}
              </a-button>
            </template>
          </a-input>
        </div>
      </div>

      <!-- 激活码输入（注册时需要） -->
      <div class="form-item center-form-item">
        <a-input
          v-model:value="formData.activationCode"
          placeholder="首次使用需输入设备ID绑定"
          size="large"
          class="form-input"
        />
      </div>

      <!-- 登录/注册按钮 -->
      <div class="form-item center-form-item">
        <a-button
          type="primary"
          size="large"
          :loading="authStore.loading"
          :disabled="!canSubmit"
          @click="handleSubmit"
          class="submit-btn"
        >
          登录/注册
        </a-button>
      </div>

      <!-- 服务协议提示 -->
      <div class="agreement-text">
        登录即代表同意服务协议，若未注册过登录后系统会自动注册一个新账号
      </div>

      <!-- 分割线 -->
      <a-divider class="divider-text">其它方式登录</a-divider>

      <!-- 微信登录 -->
      <div class="form-item wechat-login-container">
        <a-button @click="handleWechatLogin" class="wechat-login-btn" shape="circle">
          <WechatOutlined class="wechat-icon" />
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, computed, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { WechatOutlined } from '@ant-design/icons-vue'
import { useAuthStore } from '@/stores/auth'
import { PHONE_REGEX, SMS_COOLDOWN_TIME, WECHAT_CONFIG } from '@/utils/constants'

interface Props {
  modelValue: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const authStore = useAuthStore()

// 弹窗显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// 表单数据
const formData = ref({
  phone: '',
  smsCode: '',
  activationCode: '',
})

// 短信验证码相关
const sendingSms = ref(false)
const countdown = ref(0)
const countdownTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const canSendSms = computed(() => {
  return PHONE_REGEX.test(formData.value.phone) && countdown.value === 0
})

const canSubmit = computed(() => {
  return PHONE_REGEX.test(formData.value.phone) && formData.value.smsCode.length === 6
})

const smsButtonText = computed(() => {
  return countdown.value > 0 ? `${countdown.value}s` : '发送验证码'
})

// 发送短信验证码
const handleSendSms = async () => {
  if (!canSendSms.value) return

  sendingSms.value = true
  try {
    await authStore.sendSmsCode(formData.value.phone)
    message.success('验证码发送成功')

    // 开始倒计时
    countdown.value = SMS_COOLDOWN_TIME
    countdownTimer.value = setInterval(() => {
      countdown.value--
      if (countdown.value <= 0) {
        clearInterval(countdownTimer.value!)
        countdownTimer.value = null
      }
    }, 1000)
  } catch (error) {
    console.error('发送验证码失败:', error)
  } finally {
    sendingSms.value = false
  }
}

// 提交表单（登录或注册）
const handleSubmit = async () => {
  if (!canSubmit.value) return

  try {
    if (formData.value.activationCode) {
      // 注册
      await authStore.register(
        formData.value.phone,
        formData.value.smsCode,
        formData.value.activationCode,
      )
      message.success('注册成功')
    } else {
      // 登录
      await authStore.login(formData.value.phone, formData.value.smsCode)
      message.success('登录成功')
    }

    emit('success')
    handleCancel()
  } catch (error) {
    console.error('操作失败:', error)
  }
}

// 微信登录
const handleWechatLogin = () => {
  if (!WECHAT_CONFIG.APP_ID) {
    message.error('微信登录配置错误')
    return
  }

  const redirectUri = encodeURIComponent(WECHAT_CONFIG.REDIRECT_URI)
  const wechatUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${WECHAT_CONFIG.APP_ID}&redirect_uri=${redirectUri}&response_type=code&scope=snsapi_userinfo&state=login#wechat_redirect`

  window.location.href = wechatUrl
}

// 取消弹窗
const handleCancel = () => {
  visible.value = false
  // 重置表单
  formData.value = {
    phone: '',
    smsCode: '',
    activationCode: '',
  }
  // 清理倒计时
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
    countdownTimer.value = null
    countdown.value = 0
  }
}

// 清理定时器
onUnmounted(() => {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value)
  }
})
</script>

<style>
/* 设置登录弹窗的圆角为25px - 全局样式 */
.login-modal.ant-modal .ant-modal-content {
  border-radius: 25px !important;
  overflow: hidden;
}
</style>

<style scoped>
.login-modal-header {
  display: flex;
  justify-content: center;
}

.login-modal-header-label {
  font-weight: bold;
  font-size: 20px;
  color: black;
  margin: 0;
}

.login-modal-content {
  padding: 30px 0 20px 0;
}

.form-item {
  margin-bottom: 24px;
}

.center-form-item {
  display: flex;
  justify-content: center;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
}

.optional-text {
  font-size: 12px;
  color: #6b7280;
  font-weight: normal;
}

.form-input {
  width: 300px;
  height: 70px;
  border-radius: 25px;
  border: 1px solid #e8e8e8;
  font-size: 14px;
  color: #6b7280;
  background: #f7f7f7;
}

.form-input:focus {
  border-color: #52c41a;
  box-shadow: none;
  background: white;
}

.verification-input {
  display: flex;
  gap: 12px;
}

.code-input {
  flex: 1;
  height: 50px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  font-size: 16px;
  background: #f7f7f7;
}

.code-input:focus {
  border-color: #52c41a;
  box-shadow: none;
  background: white;
}

.send-code-btn {
  color: #1b7846;
  background: white;
  font-size: 14px;
}

.send-code-btn:hover:not(:disabled) {
  background: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
}

.send-code-btn:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

.submit-btn {
  width: 300px;
  height: 80px;
  background: #1b7846;
  border-radius: 25px;
  font-size: 16px;
  color: white;
  font-weight: bold;
}

.submit-btn:hover:not(:disabled) {
  background: #73d13d;
  border-color: #73d13d;
}

.agreement-text {
  text-align: center;
  font-size: 12px;
  color: #8c8c8c;
  margin: 16px 0 24px 0;
  line-height: 1.4;
}

.divider-text {
  background: white;
  padding: 0 16px;
  font-size: 14px;
}

.wechat-login-container {
  display: flex;
  justify-content: center;
  margin-bottom: 0;
}

.wechat-login-btn {
  width: 60px;
  height: 60px;
  border: none;
  color: #52c41a;
  background: #f7f7f7;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wechat-login-btn:hover {
  background: #f6ffed;
  border-color: #52c41a;
  color: #52c41a;
}

.wechat-icon {
  font-size: 24px;
}
</style>
